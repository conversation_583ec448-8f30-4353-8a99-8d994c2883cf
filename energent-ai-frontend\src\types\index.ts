// AI Provider Types
export type AIProvider = 'gemini' | 'openai';

export interface AIConfig {
  provider: AIProvider;
  apiKey: string;
  model?: string;
}

// Live API Types (from Google Gemini)
export interface LiveClientOptions {
  apiKey: string;
  model?: string;
}

export interface AudioConfig {
  sampleRate: number;
  channels: number;
  encoding: string;
}

export interface VideoConfig {
  width: number;
  height: number;
  frameRate: number;
}

// Application Types
export interface NavigationItem {
  name: string;
  href: string;
  icon?: string;
  external?: boolean;
}

export interface Feature {
  id: string;
  title: string;
  description: string;
  icon: string;
  image?: string;
  benefits?: string[];
}

export interface Application {
  id: string;
  title: string;
  description: string;
  image: string;
  href: string;
  features: string[];
}

export interface TeamMember {
  id: string;
  name: string;
  role: string;
  image: string;
  linkedin?: string;
  bio?: string;
}

export interface Statistic {
  id: string;
  label: string;
  value: string;
  description: string;
  icon?: string;
}

export interface PricingPlan {
  id: string;
  name: string;
  description: string;
  price: {
    monthly: number;
    annually: number;
  };
  features: string[];
  popular?: boolean;
  cta: string;
}

export interface Review {
  id: string;
  content: string;
  author: {
    name: string;
    role: string;
    company: string;
    image: string;
  };
  rating?: number;
}

export interface ContactForm {
  name: string;
  email: string;
  phone?: string;
  message: string;
}

// Chat Types
export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  type?: 'text' | 'audio' | 'image';
}

export interface ChatSession {
  id: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  provider: AIProvider;
}

// Animation Types
export interface AnimationConfig {
  duration: number;
  delay?: number;
  easing?: string;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: AppError;
  message?: string;
}

// Store Types
export interface AppState {
  aiProvider: AIProvider;
  isLoading: boolean;
  error: AppError | null;
  chatSessions: ChatSession[];
  currentSession: ChatSession | null;
}

export interface UIState {
  sidebarOpen: boolean;
  mobileMenuOpen: boolean;
  theme: 'light' | 'dark';
  notifications: Notification[];
}

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
}

// Component Props Types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'textarea' | 'select' | 'checkbox';
  placeholder?: string;
  required?: boolean;
  options?: { label: string; value: string }[];
  validation?: {
    pattern?: RegExp;
    minLength?: number;
    maxLength?: number;
    custom?: (value: any) => boolean | string;
  };
}

export interface FormState {
  values: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isSubmitting: boolean;
  isValid: boolean;
}
