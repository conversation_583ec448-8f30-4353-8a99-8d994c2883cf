import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, Users, Clock, DollarSign } from 'lucide-react';

interface StatisticsProps {
  className?: string;
}

const Statistics: React.FC<StatisticsProps> = ({ className = '' }) => {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);

  const statistics = [
    {
      id: 'projects',
      label: 'Projects completed',
      value: 20,
      suffix: '+',
      description: "We've successfully completed 20+ top-tier projects.",
      icon: CheckCircle,
      color: 'from-blue-500 to-cyan-500'
    },
    {
      id: 'satisfaction',
      label: 'Satisfied customers',
      value: 95,
      suffix: '%',
      description: 'We ensure a 95% satisfaction level for our clients.',
      icon: Users,
      color: 'from-green-500 to-emerald-500'
    },
    {
      id: 'time-saved',
      label: 'Hours saved per day',
      value: 3,
      suffix: 'h',
      description: 'Our solutions save our clients an average of 3 hours of work per day.',
      icon: Clock,
      color: 'from-purple-500 to-pink-500'
    },
    {
      id: 'cost-saved',
      label: 'Cost saved per month',
      value: 80,
      suffix: 'k',
      description: 'Our solutions save our clients an average of $80,000 per month.',
      icon: DollarSign,
      color: 'from-orange-500 to-red-500'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const AnimatedCounter: React.FC<{ 
    value: number; 
    suffix: string; 
    duration?: number;
  }> = ({ value, suffix, duration = 2000 }) => {
    const [count, setCount] = useState(0);

    useEffect(() => {
      if (!isVisible) return;

      let startTime: number;
      const animate = (currentTime: number) => {
        if (!startTime) startTime = currentTime;
        const progress = Math.min((currentTime - startTime) / duration, 1);
        
        setCount(Math.floor(progress * value));
        
        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      };

      requestAnimationFrame(animate);
    }, [isVisible, value, duration]);

    return (
      <span className="text-4xl md:text-5xl lg:text-6xl font-bold">
        {count}{suffix}
      </span>
    );
  };

  return (
    <section
      ref={sectionRef}
      id="statistics"
      className={`section-padding bg-gradient-to-br from-secondary-900 via-secondary-800 to-primary-900 text-white ${className}`}
    >
      <div className="container-max">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Our Statistics
          </h2>
          <p className="text-xl text-secondary-300 max-w-3xl mx-auto">
            Numbers that speak to our commitment to excellence and the tangible 
            impact we deliver for our clients across various industries.
          </p>
        </motion.div>

        {/* Statistics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {statistics.map((stat, index) => (
            <motion.div
              key={stat.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:-translate-y-2 hover:shadow-2xl">
                {/* Icon */}
                <div className={`w-16 h-16 bg-gradient-to-br ${stat.color} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <stat.icon className="w-8 h-8 text-white" />
                </div>

                {/* Value */}
                <div className="mb-4">
                  <div className="text-white">
                    <AnimatedCounter 
                      value={stat.value} 
                      suffix={stat.suffix}
                      duration={2000 + index * 200}
                    />
                  </div>
                </div>

                {/* Label */}
                <h3 className="text-xl font-semibold text-white mb-3">
                  {stat.label}
                </h3>

                {/* Description */}
                <p className="text-secondary-300 text-sm leading-relaxed">
                  {stat.description}
                </p>

                {/* Hover Effect Line */}
                <div className="mt-6 h-1 bg-gradient-to-r from-transparent via-white/30 to-transparent transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500" />
              </div>
            </motion.div>
          ))}
        </div>

        {/* Additional Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          viewport={{ once: true }}
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">
              <AnimatedCounter value={100} suffix="%" duration={2500} />
            </div>
            <p className="text-secondary-300">Uptime Guarantee</p>
          </div>
          
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">
              <AnimatedCounter value={24} suffix="/7" duration={2500} />
            </div>
            <p className="text-secondary-300">Support Available</p>
          </div>
          
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">
              <AnimatedCounter value={50} suffix="+" duration={2500} />
            </div>
            <p className="text-secondary-300">Enterprise Clients</p>
          </div>
        </motion.div>

        {/* Background Decorations */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-primary-500/20 rounded-full blur-3xl animate-float" />
          <div className="absolute bottom-1/4 right-1/4 w-40 h-40 bg-accent-500/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }} />
          <div className="absolute top-3/4 left-3/4 w-24 h-24 bg-secondary-500/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '4s' }} />
        </div>
      </div>
    </section>
  );
};

export default Statistics;
