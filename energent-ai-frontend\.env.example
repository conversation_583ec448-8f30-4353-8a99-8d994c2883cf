# AI API Configuration
REACT_APP_GEMINI_API_KEY=your_gemini_api_key_here
REACT_APP_OPENAI_API_KEY=your_openai_api_key_here

# AI Provider Selection (gemini or openai)
REACT_APP_AI_PROVIDER=gemini

# Application Configuration
REACT_APP_APP_NAME=Energent AI Frontend
REACT_APP_APP_VERSION=1.0.0
REACT_APP_API_BASE_URL=https://api.energent.ai

# Contact Information
REACT_APP_CONTACT_EMAIL=<EMAIL>
REACT_APP_SUPPORT_EMAIL=<EMAIL>

# Social Media Links
REACT_APP_TWITTER_URL=https://x.com/energentai
REACT_APP_LINKEDIN_URL=https://www.linkedin.com/company/energent-ai/

# Feature Flags
REACT_APP_ENABLE_VOICE_CHAT=true
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_ENABLE_DEBUG=false
