import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, MessageSquare, <PERSON><PERSON>, Brain } from 'lucide-react';
import { LiveAPIProvider } from '../../contexts/LiveAPIContext';
import { Altair } from '../altair/Altair';
import ControlTray from '../control-tray/ControlTray';
import { aiService } from '../../services/aiService';
import { AIProvider } from '../../types';

interface AIIntegrationHubProps {
  className?: string;
}

const AIIntegrationHub: React.FC<AIIntegrationHubProps> = ({ className = '' }) => {
  const [selectedProvider, setSelectedProvider] = useState<AIProvider>('gemini');
  const [isVoiceChatActive, setIsVoiceChatActive] = useState(false);
  const [chatMessages, setChatMessages] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const videoRef = React.useRef<HTMLVideoElement>(null);
  const [videoStream, setVideoStream] = useState<MediaStream | null>(null);

  useEffect(() => {
    const availableProviders = aiService.getAvailableProviders();
    if (availableProviders.length > 0 && !availableProviders.includes(selectedProvider)) {
      setSelectedProvider(availableProviders[0]);
    }
  }, [selectedProvider]);

  const handleProviderChange = (provider: AIProvider) => {
    setSelectedProvider(provider);
    aiService.setProvider(provider);
    setError(null);
  };

  const handleVoiceChatToggle = async () => {
    if (isVoiceChatActive) {
      setIsVoiceChatActive(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      if (selectedProvider === 'openai') {
        const cleanup = await aiService.startVoiceChat(
          (message) => {
            setChatMessages(prev => [...prev, `AI: ${message}`]);
          },
          (error) => {
            setError(error.message);
            setIsVoiceChatActive(false);
          },
          'openai'
        );

        setIsVoiceChatActive(true);
        
        // Store cleanup function for later use
        (window as any).voiceChatCleanup = cleanup;
      } else {
        // For Gemini, the voice chat is handled by the LiveAPIContext
        setIsVoiceChatActive(true);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start voice chat');
    } finally {
      setIsLoading(false);
    }
  };

  const availableProviders = aiService.getAvailableProviders();
  const geminiApiKey = process.env.REACT_APP_GEMINI_API_KEY;

  const apiOptions = {
    apiKey: geminiApiKey || '',
  };

  return (
    <section
      id="ai-hub"
      className={`section-padding bg-gradient-to-br from-secondary-50 to-white ${className}`}
    >
      <div className="container-max">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-secondary-900 mb-6">
            AI Integration Hub
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
            Experience the power of AI with our integrated voice chat system. 
            Switch between Google Gemini and OpenAI to explore different AI capabilities.
          </p>
        </motion.div>

        {/* Provider Selection */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="flex justify-center mb-8"
        >
          <div className="bg-white rounded-xl p-2 shadow-lg border border-secondary-200">
            <div className="flex space-x-2">
              {availableProviders.map((provider) => (
                <button
                  key={provider}
                  onClick={() => handleProviderChange(provider)}
                  className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 ${
                    selectedProvider === provider
                      ? 'bg-primary-600 text-white shadow-md'
                      : 'text-secondary-600 hover:bg-secondary-100'
                  }`}
                >
                  {provider === 'gemini' ? (
                    <Brain className="w-4 h-4" />
                  ) : (
                    <Zap className="w-4 h-4" />
                  )}
                  <span className="capitalize">{provider}</span>
                </button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Error Display */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 max-w-2xl mx-auto"
          >
            <p className="text-red-700 text-center">{error}</p>
          </motion.div>
        )}

        {/* AI Chat Interface */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="max-w-6xl mx-auto"
        >
          {selectedProvider === 'gemini' && geminiApiKey ? (
            <LiveAPIProvider options={apiOptions}>
              <div className="bg-white rounded-2xl shadow-xl border border-secondary-200 overflow-hidden">
                <div className="p-6 border-b border-secondary-200 bg-gradient-to-r from-primary-50 to-accent-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                        <Brain className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-secondary-900">
                          Google Gemini Live API
                        </h3>
                        <p className="text-sm text-secondary-600">
                          Real-time voice and video AI interaction
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                      <span className="text-sm text-secondary-600">Connected</span>
                    </div>
                  </div>
                </div>
                
                <div className="relative">
                  <div className="min-h-[400px] flex items-center justify-center">
                    <Altair />
                    <video
                      ref={videoRef}
                      className={`absolute inset-0 w-full h-full object-cover ${
                        !videoRef.current || !videoStream ? 'hidden' : ''
                      }`}
                      autoPlay
                      playsInline
                    />
                  </div>
                  
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent p-6">
                    <ControlTray
                      videoRef={videoRef}
                      supportsVideo={true}
                      onVideoStreamChange={setVideoStream}
                      enableEditingSettings={true}
                    />
                  </div>
                </div>
              </div>
            </LiveAPIProvider>
          ) : (
            <div className="bg-white rounded-2xl shadow-xl border border-secondary-200 overflow-hidden">
              <div className="p-6 border-b border-secondary-200 bg-gradient-to-r from-accent-50 to-primary-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-accent-600 rounded-lg flex items-center justify-center">
                      <Zap className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-secondary-900">
                        OpenAI Integration
                      </h3>
                      <p className="text-sm text-secondary-600">
                        Advanced text and voice AI capabilities
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${
                      availableProviders.includes('openai') ? 'bg-green-500 animate-pulse' : 'bg-red-500'
                    }`} />
                    <span className="text-sm text-secondary-600">
                      {availableProviders.includes('openai') ? 'Connected' : 'Not configured'}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="p-8">
                {availableProviders.includes('openai') ? (
                  <div className="text-center space-y-6">
                    <div className="w-24 h-24 bg-gradient-to-br from-accent-500 to-primary-500 rounded-full flex items-center justify-center mx-auto">
                      <MessageSquare className="w-12 h-12 text-white" />
                    </div>
                    
                    <div>
                      <h4 className="text-xl font-semibold text-secondary-900 mb-2">
                        Voice Chat with OpenAI
                      </h4>
                      <p className="text-secondary-600 mb-6">
                        Start a voice conversation with OpenAI's advanced language model
                      </p>
                    </div>
                    
                    <button
                      onClick={handleVoiceChatToggle}
                      disabled={isLoading}
                      className={`inline-flex items-center space-x-3 px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                        isVoiceChatActive
                          ? 'bg-red-600 hover:bg-red-700 text-white'
                          : 'bg-accent-600 hover:bg-accent-700 text-white'
                      } ${isLoading ? 'opacity-50 cursor-not-allowed' : 'transform hover:scale-105'}`}
                    >
                      {isLoading ? (
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      ) : isVoiceChatActive ? (
                        <MicOff className="w-5 h-5" />
                      ) : (
                        <Mic className="w-5 h-5" />
                      )}
                      <span>
                        {isLoading ? 'Connecting...' : isVoiceChatActive ? 'Stop Voice Chat' : 'Start Voice Chat'}
                      </span>
                    </button>
                    
                    {chatMessages.length > 0 && (
                      <div className="mt-8 max-h-40 overflow-y-auto bg-secondary-50 rounded-lg p-4">
                        {chatMessages.map((message, index) => (
                          <div key={index} className="text-sm text-secondary-700 mb-2">
                            {message}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center space-y-4">
                    <div className="w-16 h-16 bg-secondary-200 rounded-full flex items-center justify-center mx-auto">
                      <Settings className="w-8 h-8 text-secondary-500" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-secondary-900 mb-2">
                        OpenAI Not Configured
                      </h4>
                      <p className="text-secondary-600">
                        Please add your OpenAI API key to the environment variables to enable this feature.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </motion.div>

        {/* Feature Cards */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-16"
        >
          <div className="card text-center">
            <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Mic className="w-6 h-6 text-primary-600" />
            </div>
            <h3 className="text-lg font-semibold text-secondary-900 mb-2">Voice Interaction</h3>
            <p className="text-secondary-600">Natural voice conversations with AI assistants</p>
          </div>
          
          <div className="card text-center">
            <div className="w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Brain className="w-6 h-6 text-accent-600" />
            </div>
            <h3 className="text-lg font-semibold text-secondary-900 mb-2">Multi-Provider</h3>
            <p className="text-secondary-600">Switch between different AI providers seamlessly</p>
          </div>
          
          <div className="card text-center">
            <div className="w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Zap className="w-6 h-6 text-secondary-600" />
            </div>
            <h3 className="text-lg font-semibold text-secondary-900 mb-2">Real-time</h3>
            <p className="text-secondary-600">Instant responses and live audio processing</p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default AIIntegrationHub;
