import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Check, Star, ArrowRight, Zap, Users, Building } from 'lucide-react';

interface PricingProps {
  className?: string;
}

const Pricing: React.FC<PricingProps> = ({ className = '' }) => {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'annually'>('monthly');

  const pricingPlans = [
    {
      id: 'admin',
      name: 'Admin',
      description: 'Ideal for simple routine workflow, such as data entry, in HR, Finance, Operation, and any Admin department.',
      icon: Users,
      price: {
        monthly: 199,
        annually: 1990
      },
      popular: false,
      features: [
        '20k agent credits with unlimited workflows',
        '2k pages of document reading and unlimited webpages reading',
        'Hosted by Energent.ai',
        'Multi-step workflows for accurate data entry',
        '7/24 monitoring and executions',
        'No-code visual workflow builder',
        'Unlimited users'
      ],
      cta: 'Start free trial',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      id: 'executive',
      name: 'Executive',
      description: 'Functional as a team of Engineers, Analysts and Admins to get work done',
      icon: Zap,
      price: {
        monthly: 499,
        annually: 4990
      },
      popular: true,
      features: [
        '100k agent credits with unlimited workflows',
        '10k pages of document reading and unlimited webpages reading',
        'Hosted by Energent.ai',
        'Everything in the Admin plan, plus:',
        'Coding for Data Engineering/Cleaning Tasks',
        'Up to 10k Document Pages Extraction',
        'Up to 10G Storage',
        'Customized Routine Workflows',
        'Dedicated support with SLA',
        'Invoice billing'
      ],
      cta: 'Start free trial',
      color: 'from-purple-500 to-pink-500'
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      description: 'Ideal for businesses with strict security and performance requirements',
      icon: Building,
      price: {
        monthly: null,
        annually: null
      },
      popular: false,
      features: [
        'up to ∞ workflow executions with unlimited steps',
        'up to ∞ active workflows and unlimited test ones',
        'Self-hosted (or hosted by Energent.ai)',
        'Everything in the Executive plan, plus:',
        'Unlimited shared projects',
        '100+ concurrent executions',
        'SSO SAML and LDAP',
        'Multiple environments',
        'External secret store integration',
        'Log streaming',
        'Version control using Git',
        'Scaling options',
        'Extended data retention'
      ],
      cta: 'Contact us',
      color: 'from-orange-500 to-red-500'
    }
  ];

  const getPrice = (plan: typeof pricingPlans[0]) => {
    if (plan.price.monthly === null) return 'Contact us';
    const price = billingCycle === 'monthly' ? plan.price.monthly : plan.price.annually;
    return `$${price}`;
  };

  const getPeriod = () => {
    return billingCycle === 'monthly' ? 'per month' : 'per year';
  };

  return (
    <section
      id="pricing"
      className={`section-padding bg-gradient-to-br from-secondary-50 to-white ${className}`}
    >
      <div className="container-max">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-secondary-900 mb-6">
            Pricing
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto mb-8">
            Three different subscriptions to match your companies' needs.
          </p>

          {/* Billing Toggle */}
          <div className="inline-flex items-center bg-white rounded-xl p-2 shadow-lg border border-secondary-200">
            <button
              onClick={() => setBillingCycle('monthly')}
              className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 ${
                billingCycle === 'monthly'
                  ? 'bg-primary-600 text-white shadow-md'
                  : 'text-secondary-600 hover:bg-secondary-100'
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingCycle('annually')}
              className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 ${
                billingCycle === 'annually'
                  ? 'bg-primary-600 text-white shadow-md'
                  : 'text-secondary-600 hover:bg-secondary-100'
              }`}
            >
              Annually
            </button>
          </div>
        </motion.div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {pricingPlans.map((plan, index) => (
            <motion.div
              key={plan.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              className={`relative group ${plan.popular ? 'lg:-mt-4' : ''}`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                  <div className="bg-gradient-to-r from-accent-500 to-primary-500 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                    <Star className="w-3 h-3" />
                    <span>Most Popular</span>
                  </div>
                </div>
              )}

              <div className={`bg-white rounded-2xl shadow-xl border-2 transition-all duration-500 transform hover:-translate-y-2 hover:shadow-2xl h-full ${
                plan.popular ? 'border-primary-500 lg:scale-105' : 'border-secondary-200'
              }`}>
                <div className="p-8">
                  {/* Plan Header */}
                  <div className="text-center mb-8">
                    <div className={`w-16 h-16 bg-gradient-to-br ${plan.color} rounded-xl flex items-center justify-center mx-auto mb-4`}>
                      {React.createElement(plan.icon, { className: "w-8 h-8 text-white" })}
                    </div>
                    <h3 className="text-2xl font-bold text-secondary-900 mb-2">
                      {plan.name}
                    </h3>
                    <p className="text-secondary-600 text-sm leading-relaxed">
                      {plan.description}
                    </p>
                  </div>

                  {/* Price */}
                  <div className="text-center mb-8">
                    <div className="text-4xl font-bold text-secondary-900 mb-2">
                      {getPrice(plan)}
                      {plan.price.monthly !== null && (
                        <span className="text-lg font-normal text-secondary-500 ml-1">
                          {getPeriod()}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-4 mb-8">
                    {plan.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-start space-x-3">
                        <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Check className="w-3 h-3 text-green-600" />
                        </div>
                        <span className="text-secondary-700 text-sm leading-relaxed">
                          {feature}
                        </span>
                      </div>
                    ))}
                  </div>

                  {/* CTA Button */}
                  <button
                    onClick={() => {
                      if (plan.id === 'enterprise') {
                        window.open('mailto:<EMAIL>?subject=Enterprise Plan Inquiry', '_blank');
                      } else {
                        window.open('https://app.energent.ai/', '_blank');
                      }
                    }}
                    className={`w-full py-3 px-6 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2 ${
                      plan.popular
                        ? 'bg-primary-600 hover:bg-primary-700 text-white shadow-lg'
                        : 'bg-secondary-100 hover:bg-secondary-200 text-secondary-800'
                    }`}
                  >
                    <span>{plan.cta}</span>
                    <ArrowRight className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom Note */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <p className="text-secondary-600 mb-4">
            All plans include a 14-day free trial. No credit card required.
          </p>
          <div className="flex flex-wrap justify-center items-center space-x-8 text-sm text-secondary-500">
            <span>✓ Cancel anytime</span>
            <span>✓ 24/7 support</span>
            <span>✓ 99.9% uptime SLA</span>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Pricing;
