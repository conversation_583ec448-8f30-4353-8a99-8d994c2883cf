{"name": "energent-ai-frontend", "version": "1.0.0", "private": true, "description": "A modern React TypeScript frontend application with AI-powered features, inspired by Energent.ai", "author": "<PERSON><PERSON> (chirag127)", "homepage": "https://github.com/chirag127/Frontend-Engineer-2/tree/main/energent-ai-frontend", "repository": {"type": "git", "url": "https://github.com/chirag127/Frontend-Engineer-2.git"}, "bugs": {"url": "https://github.com/chirag127/Frontend-Engineer-2/issues"}, "changelog": "https://github.com/chirag127/Frontend-Engineer-2/blob/main/energent-ai-frontend/CHANGELOG.md", "dependencies": {"@google/genai": "^1.10.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/lodash": "^4.17.20", "@types/node": "^16.18.126", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "@types/react-syntax-highlighter": "^15.5.13", "classnames": "^2.5.1", "dotenv-flow": "^4.1.0", "eventemitter3": "^5.0.1", "framer-motion": "^12.23.6", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "openai": "^5.10.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.7.0", "react-scripts": "5.0.1", "react-select": "^5.10.2", "react-syntax-highlighter": "^15.6.1", "sass": "^1.89.2", "typescript": "^4.9.5", "vega": "^6.1.2", "vega-embed": "^7.0.2", "vega-lite": "^6.2.0", "web-vitals": "^2.1.4", "zustand": "^5.0.6"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "generate-assets": "node scripts/generate-assets.js", "prebuild": "npm run generate-assets", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "sharp": "^0.34.3", "tailwindcss": "^4.1.11"}}