# Energent AI Frontend

**Author: <PERSON><PERSON> (chirag127)**

A modern React TypeScript frontend application with AI-powered features, inspired by the Energent.ai website design and integrated with both Google Gemini Live API and OpenAI for comprehensive AI capabilities.

**Last Updated: July 20, 2025 06:59:53 UTC**

## 🚀 Live Demo

🔗 **Deployed Application**: [Coming Soon - Will be deployed to Vercel]

## 📋 Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Tech Stack](#tech-stack)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Environment Setup](#environment-setup)
- [Running the Application](#running-the-application)
- [Project Structure](#project-structure)
- [AI Integration](#ai-integration)
- [API Documentation](#api-documentation)
- [Deployment](#deployment)
- [Contributing](#contributing)
- [License](#license)

## 🎯 Overview

This project is a comprehensive frontend application that replicates the modern design and functionality of Energent.ai while adding advanced AI integration capabilities. The application features:

- **Modern UI/UX**: Clean, professional design inspired by Energent.ai
- **AI Integration Hub**: Seamless switching between Google Gemini and OpenAI
- **Voice Chat Capabilities**: Real-time voice interaction with AI assistants
- **Responsive Design**: Optimized for all device sizes
- **Interactive Components**: Engaging animations and user interactions

## ✨ Features

### Core Features
- **Hero Section**: Compelling landing page with call-to-action
- **AI Integration Hub**: Central hub for AI interactions with provider switching
- **Core Features Showcase**: Interactive feature demonstrations
- **Applications Gallery**: Specialized AI solutions for different industries
- **Statistics Dashboard**: Animated counters and performance metrics
- **Team Showcase**: Professional team member profiles
- **Pricing Plans**: Interactive subscription plans with billing toggles
- **Customer Reviews**: Carousel of client testimonials
- **Contact Form**: Functional contact form with validation
- **FAQ Section**: Expandable frequently asked questions

### AI Capabilities
- **Google Gemini Live API**: Real-time voice and video AI interaction
- **OpenAI Integration**: Advanced text and voice AI capabilities
- **Provider Switching**: Environment variable-based AI provider selection
- **Voice Chat**: Natural voice conversations with AI assistants
- **Real-time Processing**: Instant responses and live audio processing

### Technical Features
- **TypeScript**: Full type safety and developer experience
- **Tailwind CSS**: Utility-first styling with custom design system
- **Framer Motion**: Smooth animations and transitions
- **Responsive Design**: Mobile-first approach with breakpoint optimization
- **Component Architecture**: Modular, reusable component structure
- **State Management**: Zustand for efficient state handling
- **Error Handling**: Comprehensive error boundaries and validation

## 🛠 Tech Stack

### Frontend Framework
- **React 18.3.1** - Modern React with hooks and concurrent features
- **TypeScript 5.6.3** - Type-safe JavaScript development
- **Create React App** - Zero-configuration build setup

### Styling & UI
- **Tailwind CSS 3.x** - Utility-first CSS framework
- **Framer Motion** - Production-ready motion library
- **Lucide React** - Beautiful & consistent icon library
- **Custom Design System** - Energent.ai inspired color palette and components

### AI Integration
- **@google/genai 0.14.0** - Google Gemini API integration
- **OpenAI** - OpenAI API for advanced language models
- **Live API Support** - Real-time voice and video processing

### State Management & Utils
- **Zustand 5.0.1** - Lightweight state management
- **Lodash 4.17.21** - Utility functions
- **ClassNames 2.5.1** - Conditional CSS classes
- **dotenv-flow 4.1.0** - Environment variable management

### Development Tools
- **ESLint** - Code linting and quality
- **Prettier** - Code formatting
- **TypeScript Compiler** - Type checking
- **React Testing Library** - Component testing

## 📋 Prerequisites

Before running this application, ensure you have:

- **Node.js** (v16.0.0 or higher)
- **npm** (v8.0.0 or higher) or **yarn** (v1.22.0 or higher)
- **Git** for version control
- **API Keys** for AI services (optional but recommended):
  - Google Gemini API key
  - OpenAI API key

## 🚀 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/chirag127/Frontend-Engineer-2.git
   cd Frontend-Engineer-2/energent-ai-frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```

4. **Configure your API keys** (see Environment Setup section)

## 🔧 Environment Setup

Create a `.env` file in the root directory and configure the following variables:

```env
# AI API Configuration
REACT_APP_GEMINI_API_KEY=your_gemini_api_key_here
REACT_APP_OPENAI_API_KEY=your_openai_api_key_here

# AI Provider Selection (gemini or openai)
REACT_APP_AI_PROVIDER=gemini

# Application Configuration
REACT_APP_APP_NAME=Energent AI Frontend
REACT_APP_APP_VERSION=1.0.0
REACT_APP_API_BASE_URL=https://api.energent.ai

# Contact Information
REACT_APP_CONTACT_EMAIL=<EMAIL>
REACT_APP_SUPPORT_EMAIL=<EMAIL>

# Social Media Links
REACT_APP_TWITTER_URL=https://x.com/energentai
REACT_APP_LINKEDIN_URL=https://www.linkedin.com/company/energent-ai/

# Feature Flags
REACT_APP_ENABLE_VOICE_CHAT=true
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_ENABLE_DEBUG=false
```

### API Key Setup

#### Google Gemini API Key
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add it to your `.env` file as `REACT_APP_GEMINI_API_KEY`

#### OpenAI API Key
1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create a new API key
3. Add it to your `.env` file as `REACT_APP_OPENAI_API_KEY`

## 🏃‍♂️ Running the Application

### Development Mode
```bash
npm start
```
Opens the app in development mode at [http://localhost:3000](http://localhost:3000)

### Production Build
```bash
npm run build
```
Builds the app for production to the `build` folder

### Testing
```bash
npm test
```
Launches the test runner in interactive watch mode

### Linting
```bash
npm run lint
```
Runs ESLint to check for code quality issues

## 📁 Project Structure

```
energent-ai-frontend/
├── public/
│   ├── index.html
│   ├── favicon.ico
│   └── robots.txt
├── src/
│   ├── components/
│   │   ├── layout/
│   │   │   ├── Navigation.tsx
│   │   │   └── Footer.tsx
│   │   ├── sections/
│   │   │   ├── Hero.tsx
│   │   │   ├── AIIntegrationHub.tsx
│   │   │   ├── CoreFeatures.tsx
│   │   │   ├── Applications.tsx
│   │   │   ├── Statistics.tsx
│   │   │   ├── Team.tsx
│   │   │   ├── Pricing.tsx
│   │   │   ├── Reviews.tsx
│   │   │   └── Contact.tsx
│   │   ├── altair/          # Google Gemini Live API components
│   │   ├── control-tray/    # Voice chat controls
│   │   └── settings-dialog/ # AI configuration
│   ├── contexts/
│   │   └── LiveAPIContext.tsx
│   ├── hooks/
│   │   ├── use-live-api.ts
│   │   ├── use-media-stream-mux.ts
│   │   ├── use-screen-capture.ts
│   │   └── use-webcam.ts
│   ├── lib/
│   │   ├── audio-recorder.ts
│   │   ├── audio-streamer.ts
│   │   ├── genai-live-client.ts
│   │   └── utils.ts
│   ├── services/
│   │   ├── aiService.ts
│   │   └── openai.ts
│   ├── types/
│   │   └── index.ts
│   ├── App.tsx
│   ├── index.tsx
│   └── index.css
├── .env.example
├── .env
├── package.json
├── tailwind.config.js
├── tsconfig.json
└── README.md
```

## 🤖 AI Integration

### Google Gemini Live API
- **Real-time voice chat**: Natural conversation with AI
- **Video processing**: Live video analysis capabilities
- **Multimodal input**: Text, voice, and visual inputs
- **Streaming responses**: Real-time AI responses

### OpenAI Integration
- **Text completion**: Advanced language model responses
- **Voice transcription**: Speech-to-text using Whisper
- **Text-to-speech**: AI-generated voice responses
- **Streaming chat**: Real-time conversation capabilities

### Provider Switching
The application supports seamless switching between AI providers through environment variables:

```typescript
// Set in .env file
REACT_APP_AI_PROVIDER=gemini  // or 'openai'
```

## 📚 API Documentation

### AI Service Manager
The `AIServiceManager` class provides a unified interface for both AI providers:

```typescript
import { aiService } from './services/aiService';

// Send a message
const response = await aiService.sendMessage(messages, 'openai');

// Start voice chat
const cleanup = await aiService.startVoiceChat(
  (message) => console.log('AI:', message),
  (error) => console.error('Error:', error),
  'gemini'
);
```

### Environment Variables
All configuration is handled through environment variables for security and flexibility.

## 🚀 Deployment

### Vercel Deployment (Recommended)

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Deploy to Vercel**
   ```bash
   vercel --prod
   ```

3. **Configure Environment Variables**
   - Add all environment variables in Vercel dashboard
   - Ensure API keys are properly configured

### Manual Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Deploy the `build` folder** to your hosting provider

## 🤝 Contributing

This project was created as part of a coding challenge. For review purposes, please contact:

- **GitHub Reviewers**: 
  - [@remi-guan](https://github.com/remi-guan)
  - [@lingjiekong](https://github.com/lingjiekong)
  - [@goldmermaid](https://github.com/goldmermaid)
  - [@EnergentAI](https://github.com/EnergentAI)

## 📄 License

This project is created for evaluation purposes. All rights reserved.

---

**Built with ❤️ by Chirag Singhal**

For questions or support, please contact: [<EMAIL>](mailto:<EMAIL>)
