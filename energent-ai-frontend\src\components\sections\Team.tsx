import React from 'react';
import { motion } from 'framer-motion';
import { Linkedin, ExternalLink, Users, Award, Briefcase } from 'lucide-react';

interface TeamProps {
  className?: string;
}

const Team: React.FC<TeamProps> = ({ className = '' }) => {
  const teamMembers = [
    {
      id: 'jon-conradt',
      name: '<PERSON>',
      role: 'Advisor',
      image: '/images/team/jon-conradt.png',
      linkedin: 'https://www.linkedin.com/in/jonconradt/',
      bio: 'Experienced technology advisor with deep expertise in AI and machine learning.',
      background: 'from-blue-500 to-cyan-500'
    },
    {
      id: 'rachel-hu',
      name: '<PERSON>',
      role: 'Co-founder & CEO',
      image: '/images/team/rachel-hu.png',
      linkedin: 'https://www.linkedin.com/in/rachelsonghu/',
      bio: 'Visionary leader driving AI innovation and business transformation.',
      background: 'from-purple-500 to-pink-500'
    },
    {
      id: 'kimi-kong',
      name: '<PERSON><PERSON>',
      role: 'Co-founder & CTO',
      image: '/images/team/kimi-kong.png',
      linkedin: 'https://www.linkedin.com/in/0xlingjiekong/',
      bio: 'Technical architect building scalable AI solutions and platforms.',
      background: 'from-green-500 to-emerald-500'
    },
    {
      id: 'charles-yuan',
      name: 'Charles Yuan',
      role: 'Machine Learning Engineer',
      image: '/images/team/charles-yuan.png',
      linkedin: 'https://www.linkedin.com/in/boqin-yuan/',
      bio: 'ML expert developing cutting-edge algorithms and models.',
      background: 'from-orange-500 to-red-500'
    },
    {
      id: 'omar-el-ghamry',
      name: 'Omar El-Ghamry',
      role: 'Full Stack Engineer',
      image: '/images/team/omar-el-ghamry.png',
      linkedin: 'https://www.linkedin.com/in/ghamry03/',
      bio: 'Full-stack developer creating seamless user experiences.',
      background: 'from-indigo-500 to-purple-500'
    },
    {
      id: 'remi-guan',
      name: 'Remi Guan',
      role: 'Full Stack Engineer',
      image: '/images/team/remi-guan.png',
      linkedin: 'https://www.linkedin.com/in/remi-guan/',
      bio: 'Versatile engineer building robust and scalable applications.',
      background: 'from-teal-500 to-blue-500'
    }
  ];

  return (
    <section
      id="team"
      className={`section-padding bg-white ${className}`}
    >
      <div className="container-max">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-secondary-900 mb-6">
            Meet Our Team
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
            A diverse group of experts passionate about AI innovation and committed 
            to delivering exceptional solutions that transform businesses worldwide.
          </p>
        </motion.div>

        {/* Team Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {teamMembers.map((member, index) => (
            <motion.div
              key={member.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-white rounded-2xl shadow-lg border border-secondary-200 overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                {/* Profile Image */}
                <div className="relative overflow-hidden">
                  <div className={`h-64 bg-gradient-to-br ${member.background} flex items-center justify-center relative`}>
                    {/* Placeholder for member image */}
                    <div className="w-32 h-32 bg-white/20 rounded-full flex items-center justify-center">
                      <Users className="w-16 h-16 text-white" />
                    </div>
                    
                    {/* Overlay */}
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300" />
                    
                    {/* LinkedIn Button */}
                    <motion.a
                      href={member.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      initial={{ opacity: 0, scale: 0.8 }}
                      whileHover={{ scale: 1.1 }}
                      className="absolute top-4 right-4 w-10 h-10 bg-white/90 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 hover:bg-white"
                    >
                      <Linkedin className="w-5 h-5 text-blue-600" />
                    </motion.a>
                  </div>
                </div>

                {/* Member Info */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-secondary-900 mb-1">
                    {member.name}
                  </h3>
                  <p className="text-primary-600 font-medium mb-3">
                    {member.role}
                  </p>
                  <p className="text-secondary-600 text-sm leading-relaxed">
                    {member.bio}
                  </p>
                  
                  {/* Social Links */}
                  <div className="mt-4 flex items-center space-x-3">
                    <a
                      href={member.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center space-x-1 text-sm text-secondary-500 hover:text-primary-600 transition-colors duration-200"
                    >
                      <Linkedin className="w-4 h-4" />
                      <span>LinkedIn</span>
                    </a>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Team Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          <div className="text-center p-6 bg-gradient-to-br from-primary-50 to-accent-50 rounded-xl border border-primary-200">
            <div className="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Users className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-secondary-900 mb-2">6+</h3>
            <p className="text-secondary-600">Team Members</p>
          </div>
          
          <div className="text-center p-6 bg-gradient-to-br from-accent-50 to-secondary-50 rounded-xl border border-accent-200">
            <div className="w-12 h-12 bg-accent-600 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Award className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-secondary-900 mb-2">10+</h3>
            <p className="text-secondary-600">Years Combined Experience</p>
          </div>
          
          <div className="text-center p-6 bg-gradient-to-br from-secondary-50 to-primary-50 rounded-xl border border-secondary-200">
            <div className="w-12 h-12 bg-secondary-600 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Briefcase className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-secondary-900 mb-2">3</h3>
            <p className="text-secondary-600">Office Locations</p>
          </div>
        </motion.div>

        {/* Join Team CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-secondary-900 to-primary-900 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Join Our Growing Team
            </h3>
            <p className="text-secondary-300 mb-6 max-w-2xl mx-auto">
              We're always looking for talented individuals who share our passion 
              for AI innovation and want to make a meaningful impact.
            </p>
            <button
              onClick={() => window.open('mailto:<EMAIL>', '_blank')}
              className="inline-flex items-center space-x-2 bg-white text-secondary-900 font-semibold px-8 py-3 rounded-lg hover:bg-secondary-100 transition-all duration-300 transform hover:scale-105"
            >
              <span>View Open Positions</span>
              <ExternalLink className="w-4 h-4" />
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Team;
