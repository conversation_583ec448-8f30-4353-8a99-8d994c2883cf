import React from 'react';
import { motion } from 'framer-motion';
import { ExternalLink, Mail, Twitter, Linkedin, ArrowUp } from 'lucide-react';

interface FooterProps {
  className?: string;
}

const Footer: React.FC<FooterProps> = ({ className = '' }) => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const footerLinks = {
    applications: [
      { name: 'AI O&G Specialist', href: '/og' },
      { name: 'AI HR', href: '/hr' },
      { name: 'AI Data Scientist', href: '/data-science' }
    ],
    resources: [
      { name: 'Security Whitepaper', href: '/resources/security-and-data-privacy' },
      { name: 'Deployment Overview', href: '/resources/enterprise-deployment' },
      { name: 'AWS Deployment', href: '/resources/aws-deployment' },
      { name: 'Azure Deployment', href: '/resources/azure-deployment' }
    ],
    company: [
      { name: 'Templates', href: 'https://app.energent.ai/gallery' },
      { name: 'Company', href: '/company' },
      { name: 'Pricing', href: '/pricing' },
      { name: 'Blog', href: '/blog' }
    ],
    legal: [
      { name: 'Terms & conditions', href: '/terms-and-conditions' },
      { name: 'Privacy policy', href: '/privacy-policy' }
    ]
  };

  const socialLinks = [
    {
      name: 'Twitter',
      href: 'https://x.com/energentai',
      icon: Twitter
    },
    {
      name: 'LinkedIn',
      href: 'https://www.linkedin.com/company/energent-ai/',
      icon: Linkedin
    }
  ];

  return (
    <footer className={`bg-secondary-900 text-white ${className}`}>
      <div className="container-max">
        {/* Main Footer Content */}
        <div className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <div className="flex items-center space-x-2 mb-4">
                  <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">E</span>
                  </div>
                  <span className="text-xl font-bold">Energent.ai</span>
                </div>
                <p className="text-secondary-300 mb-6 leading-relaxed">
                  AI Agent turns data into actionable insights. No-code, no integration. 
                  Automation accessible without tech expertise.
                </p>
                
                {/* Social Links */}
                <div className="flex space-x-4">
                  {socialLinks.map((social) => (
                    <a
                      key={social.name}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200 group"
                    >
                      <social.icon className="w-5 h-5 text-secondary-300 group-hover:text-white" />
                    </a>
                  ))}
                </div>
              </motion.div>
            </div>

            {/* Applications */}
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <h3 className="font-semibold mb-4">Applications</h3>
                <ul className="space-y-3">
                  {footerLinks.applications.map((link) => (
                    <li key={link.name}>
                      <a
                        href={`https://energent.ai${link.href}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-secondary-300 hover:text-primary-400 transition-colors duration-200 text-sm flex items-center space-x-1"
                      >
                        <span>{link.name}</span>
                        <ExternalLink className="w-3 h-3" />
                      </a>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>

            {/* Resources */}
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <h3 className="font-semibold mb-4">Resources</h3>
                <ul className="space-y-3">
                  {footerLinks.resources.map((link) => (
                    <li key={link.name}>
                      <a
                        href={`https://energent.ai${link.href}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-secondary-300 hover:text-primary-400 transition-colors duration-200 text-sm flex items-center space-x-1"
                      >
                        <span>{link.name}</span>
                        <ExternalLink className="w-3 h-3" />
                      </a>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>

            {/* Company */}
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <h3 className="font-semibold mb-4">Company</h3>
                <ul className="space-y-3">
                  {footerLinks.company.map((link) => (
                    <li key={link.name}>
                      <a
                        href={link.href.startsWith('http') ? link.href : `https://energent.ai${link.href}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-secondary-300 hover:text-primary-400 transition-colors duration-200 text-sm flex items-center space-x-1"
                      >
                        <span>{link.name}</span>
                        <ExternalLink className="w-3 h-3" />
                      </a>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>

            {/* Legal */}
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                viewport={{ once: true }}
              >
                <h3 className="font-semibold mb-4">Legal</h3>
                <ul className="space-y-3">
                  {footerLinks.legal.map((link) => (
                    <li key={link.name}>
                      <a
                        href={`https://energent.ai${link.href}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-secondary-300 hover:text-primary-400 transition-colors duration-200 text-sm flex items-center space-x-1"
                      >
                        <span>{link.name}</span>
                        <ExternalLink className="w-3 h-3" />
                      </a>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-secondary-700 py-6 px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-secondary-400 text-sm"
            >
              © 2025, Cambio Corp. - All rights reserved.
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="flex items-center space-x-6 text-sm"
            >
              <a
                href="mailto:<EMAIL>"
                className="text-secondary-300 hover:text-primary-400 transition-colors duration-200 flex items-center space-x-1"
              >
                <Mail className="w-4 h-4" />
                <span><EMAIL></span>
              </a>
              
              <button
                onClick={scrollToTop}
                className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center hover:bg-primary-700 transition-colors duration-200 group"
              >
                <ArrowUp className="w-4 h-4 text-white group-hover:scale-110 transition-transform duration-200" />
              </button>
            </motion.div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
