const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  inputDir: path.join(__dirname, '../src/assets/icons'),
  outputDir: path.join(__dirname, '../src/assets/images'),
  sizes: [16, 24, 32, 48, 64, 128, 256, 512],
  formats: ['png', 'webp']
};

// Ensure output directory exists
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Function to convert SVG to multiple formats and sizes
async function convertSvgToImages(svgPath, outputName) {
  console.log(`Processing ${outputName}...`);
  
  try {
    const svgBuffer = fs.readFileSync(svgPath);
    
    for (const size of config.sizes) {
      for (const format of config.formats) {
        const outputPath = path.join(
          config.outputDir,
          `${outputName}-${size}.${format}`
        );
        
        await sharp(svgBuffer)
          .resize(size, size)
          .toFormat(format, {
            quality: format === 'webp' ? 90 : undefined,
            compressionLevel: format === 'png' ? 9 : undefined
          })
          .toFile(outputPath);
        
        console.log(`  Generated: ${outputName}-${size}.${format}`);
      }
    }
    
    // Also create a default size (64px) without size suffix
    for (const format of config.formats) {
      const outputPath = path.join(
        config.outputDir,
        `${outputName}.${format}`
      );
      
      await sharp(svgBuffer)
        .resize(64, 64)
        .toFormat(format, {
          quality: format === 'webp' ? 90 : undefined,
          compressionLevel: format === 'png' ? 9 : undefined
        })
        .toFile(outputPath);
      
      console.log(`  Generated: ${outputName}.${format}`);
    }
    
  } catch (error) {
    console.error(`Error processing ${outputName}:`, error.message);
  }
}

// Function to generate favicon sizes
async function generateFavicons(svgPath) {
  console.log('Generating favicons...');
  
  try {
    const svgBuffer = fs.readFileSync(svgPath);
    const faviconSizes = [16, 32, 48, 64, 128, 256];
    
    for (const size of faviconSizes) {
      const outputPath = path.join(
        __dirname,
        '../public',
        `favicon-${size}.png`
      );
      
      await sharp(svgBuffer)
        .resize(size, size)
        .png({ compressionLevel: 9 })
        .toFile(outputPath);
      
      console.log(`  Generated: favicon-${size}.png`);
    }
    
    // Generate main favicon.ico (32px)
    const faviconPath = path.join(__dirname, '../public/favicon.ico');
    await sharp(svgBuffer)
      .resize(32, 32)
      .png()
      .toFile(faviconPath.replace('.ico', '.png'));
    
    console.log('  Generated: favicon.png');
    
  } catch (error) {
    console.error('Error generating favicons:', error.message);
  }
}

// Function to generate app icons for PWA
async function generateAppIcons(svgPath) {
  console.log('Generating app icons...');
  
  try {
    const svgBuffer = fs.readFileSync(svgPath);
    const appIconSizes = [72, 96, 128, 144, 152, 192, 384, 512];
    
    for (const size of appIconSizes) {
      const outputPath = path.join(
        __dirname,
        '../public',
        `logo${size}.png`
      );
      
      await sharp(svgBuffer)
        .resize(size, size)
        .png({ compressionLevel: 9 })
        .toFile(outputPath);
      
      console.log(`  Generated: logo${size}.png`);
    }
    
  } catch (error) {
    console.error('Error generating app icons:', error.message);
  }
}

// Main execution
async function main() {
  console.log('🎨 Starting asset generation...\n');
  
  try {
    // Get all SVG files in the input directory
    const svgFiles = fs.readdirSync(config.inputDir)
      .filter(file => file.endsWith('.svg'));
    
    if (svgFiles.length === 0) {
      console.log('No SVG files found in the input directory.');
      return;
    }
    
    // Process each SVG file
    for (const svgFile of svgFiles) {
      const svgPath = path.join(config.inputDir, svgFile);
      const outputName = path.basename(svgFile, '.svg');
      
      await convertSvgToImages(svgPath, outputName);
    }
    
    // Generate favicons and app icons from logo.svg if it exists
    const logoPath = path.join(config.inputDir, 'logo.svg');
    if (fs.existsSync(logoPath)) {
      await generateFavicons(logoPath);
      await generateAppIcons(logoPath);
    }
    
    console.log('\n✅ Asset generation completed successfully!');
    console.log(`📁 Generated assets are in: ${config.outputDir}`);
    console.log(`📁 Favicons and app icons are in: ${path.join(__dirname, '../public')}`);
    
  } catch (error) {
    console.error('❌ Error during asset generation:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  convertSvgToImages,
  generateFavicons,
  generateAppIcons,
  config
};
