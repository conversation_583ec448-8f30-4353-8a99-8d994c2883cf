import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Database,
  BarChart3,
  Workflow,
  Settings,
  TrendingUp,
  Brain,
  Zap,
  Target,
  ArrowRight,
  Play
} from 'lucide-react';

interface CoreFeaturesProps {
  className?: string;
}

const CoreFeatures: React.FC<CoreFeaturesProps> = ({ className = '' }) => {
  const [activeFeature, setActiveFeature] = useState(0);

  const features = [
    {
      id: 'knowledge-hub',
      title: 'Knowledge Hub',
      description: 'We develop intelligent AI assistant acting as a single point of reference, pulling together data from multiple sources, so teams can quickly find insights.',
      icon: Database,
      image: '/images/core-features/knowledge-hub.png',
      benefits: [
        'Centralized data access',
        'Intelligent search capabilities',
        'Real-time data synchronization',
        'Multi-source integration'
      ],
      demo: {
        title: 'Generating report...',
        action: 'Generate'
      }
    },
    {
      id: 'visualization',
      title: 'Customized Visualization',
      description: 'We generate clear, real-time dashboards and graphs, turning raw data into actionable intelligence without manual crunching.',
      icon: BarChart3,
      image: '/images/core-features/visualization.png',
      benefits: [
        'Interactive dashboards',
        'Real-time updates',
        'Custom chart types',
        'Export capabilities'
      ],
      demo: {
        title: 'Creating dashboard...',
        action: 'Visualize'
      }
    },
    {
      id: 'agentic-workflow',
      title: 'Agentic Workflow',
      description: 'We take on repetitive or manual tasks (e.g., data entry, scheduling, form filling), freeing up human teams for higher-value work.',
      icon: Workflow,
      image: '/images/core-features/agentic-workflow.png',
      benefits: [
        'Automated task execution',
        'Workflow optimization',
        'Error reduction',
        'Time savings'
      ],
      demo: {
        title: 'Automating workflow...',
        action: 'Automate'
      }
    },
    {
      id: 'data-engineering',
      title: 'Data Engineering',
      description: 'We collect and organize messy information, efficiently transform raw, unstructured information into structured, reliable datasets.',
      icon: Settings,
      image: '/images/core-features/data-engineering.png',
      benefits: [
        'Data cleaning & validation',
        'ETL pipeline automation',
        'Schema standardization',
        'Quality assurance'
      ],
      demo: {
        title: 'Processing data...',
        action: 'Transform'
      }
    },
    {
      id: 'continuous-learning',
      title: 'Continuous Learning',
      description: 'We learn from daily operations and historical data to improve recommendations, making it more valuable over time.',
      icon: TrendingUp,
      image: '/images/core-features/continuous-learning.png',
      benefits: [
        'Adaptive algorithms',
        'Performance optimization',
        'Pattern recognition',
        'Predictive insights'
      ],
      demo: {
        title: 'Learning patterns...',
        action: 'Optimize'
      }
    }
  ];

  const integrationLogos = [
    { name: 'Chrome', src: '/images/logos/chrome.png' },
    { name: 'Excel', src: '/images/logos/excel.png' },
    { name: 'Outlook', src: '/images/logos/outlook.png' },
    { name: 'Word', src: '/images/logos/word.png' },
    { name: 'SAP', src: '/images/logos/sap.png' },
    { name: 'Tableau', src: '/images/logos/tableau.png' },
    { name: 'IBM', src: '/images/logos/ibm.png' },
    { name: 'GE', src: '/images/logos/ge.png' },
    { name: 'Oracle', src: '/images/logos/oracle.png' },
    { name: 'Figma', src: '/images/logos/figma.png' },
  ];

  return (
    <section
      id="features"
      className={`section-padding bg-white ${className}`}
    >
      <div className="container-max">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-secondary-900 mb-6">
            Core Features
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
            Powerful AI-driven capabilities that transform how your business operates,
            making complex tasks simple and data-driven decisions effortless.
          </p>
        </motion.div>

        {/* Feature Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {features.map((feature, index) => (
            <button
              key={feature.id}
              onClick={() => setActiveFeature(index)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                activeFeature === index
                  ? 'bg-primary-600 text-white shadow-lg transform scale-105'
                  : 'bg-secondary-100 text-secondary-700 hover:bg-secondary-200'
              }`}
            >
              {React.createElement(feature.icon, { className: "w-5 h-5" })}
              <span>{feature.title}</span>
            </button>
          ))}
        </motion.div>

        {/* Active Feature Display */}
        <motion.div
          key={activeFeature}
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16"
        >
          {/* Feature Content */}
          <div className="space-y-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                {React.createElement(features[activeFeature].icon, { className: "w-6 h-6 text-primary-600" })}
              </div>
              <h3 className="text-2xl font-bold text-secondary-900">
                {features[activeFeature].title}
              </h3>
            </div>

            <p className="text-lg text-secondary-600 leading-relaxed">
              {features[activeFeature].description}
            </p>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {features[activeFeature].benefits.map((benefit, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-primary-600 rounded-full" />
                  <span className="text-secondary-700">{benefit}</span>
                </div>
              ))}
            </div>

            <button className="inline-flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-300 transform hover:scale-105">
              <span>Learn More</span>
              <ArrowRight className="w-4 h-4" />
            </button>
          </div>

          {/* Feature Demo */}
          <div className="relative">
            <div className="bg-gradient-to-br from-secondary-50 to-primary-50 rounded-2xl p-8 border border-secondary-200">
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-semibold text-secondary-900">
                    {features[activeFeature].demo.title}
                  </h4>
                  <button className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                    {features[activeFeature].demo.action}
                  </button>
                </div>

                <div className="space-y-3">
                  <div className="h-4 bg-secondary-200 rounded animate-pulse" />
                  <div className="h-4 bg-secondary-200 rounded animate-pulse w-3/4" />
                  <div className="h-4 bg-secondary-200 rounded animate-pulse w-1/2" />
                </div>

                <div className="mt-6 flex items-center justify-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-500 rounded-full flex items-center justify-center">
                    <Brain className="w-8 h-8 text-white animate-pulse" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Integration Logos */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h3 className="text-2xl font-bold text-secondary-900 mb-8">
            Seamless Integration with Your Favorite Tools
          </h3>

          <div className="relative overflow-hidden">
            <div className="flex animate-scroll space-x-8">
              {[...integrationLogos, ...integrationLogos].map((logo, index) => (
                <div
                  key={`${logo.name}-${index}`}
                  className="flex-shrink-0 w-16 h-16 bg-white rounded-lg shadow-md border border-secondary-200 flex items-center justify-center grayscale hover:grayscale-0 transition-all duration-300"
                >
                  <div className="w-8 h-8 bg-secondary-300 rounded" />
                </div>
              ))}
            </div>
          </div>

          <div className="mt-8 bg-gradient-to-r from-primary-50 to-accent-50 rounded-xl p-6 border border-primary-200">
            <div className="flex items-center justify-center space-x-4">
              <div className="flex items-center space-x-2">
                <Target className="w-5 h-5 text-primary-600" />
                <span className="font-semibold text-secondary-900">100+</span>
              </div>
              <span className="text-secondary-600">Automations</span>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default CoreFeatures;
