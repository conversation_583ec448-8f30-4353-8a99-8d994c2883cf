.react-select {
  background: var(--Neutral-20);
  color: var(--Neutral-90);
  width: 193px;
  height: 30px;

  .react-select__single-value {
    color: var(--Neutral-90);
  }

  .react-select__menu {
    background: var(--Neutral-20);
    color: var(--Neutral-90);
  }

  .react-select__option {
  }

  .react-select__value-container {
  }

  .react-select__indicators {
  }

  .react-select__option:hover,
  .react-select__option:focus,
  .react-select_option:focus-within {
    background: var(--Neutral-30);
  }

  .react-select__option--is-focused: {
    background: var(--Neutral-30);
  }
}
