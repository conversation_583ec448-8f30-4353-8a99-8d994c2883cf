import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON>hart, Zap, ArrowRight, ExternalLink } from 'lucide-react';

interface ApplicationsProps {
  className?: string;
}

const Applications: React.FC<ApplicationsProps> = ({ className = '' }) => {
  const applications = [
    {
      id: 'ai-hr',
      number: '01',
      title: 'AI HR',
      description: 'Handles repetitive HR workflows, enhances productivity, and reduces manual errors — while keeping sensitive employee data private and secure. Allow simultaneously screening hundreds of applicants without human intervention.',
      icon: Users,
      image: '/images/applications/ai-hr.png',
      href: '/hr',
      features: [
        'Automated candidate screening',
        'Resume parsing & analysis',
        'Interview scheduling',
        'Compliance monitoring'
      ],
      color: 'from-blue-500 to-cyan-500'
    },
    {
      id: 'ai-data-scientist',
      number: '02',
      title: 'AI Data Scientist',
      description: 'Accelerates data workflows at scale across Excel, SQL clients, browser dashboards, and notebooks — all on one shared desktop. No code. No maintenance. Data cleaned.',
      icon: <PERSON><PERSON><PERSON>,
      image: '/images/applications/ai-data-scientist.png',
      href: '/data-science',
      features: [
        'Automated data analysis',
        'Predictive modeling',
        'Report generation',
        'Data visualization'
      ],
      color: 'from-purple-500 to-pink-500'
    },
    {
      id: 'ai-og-specialist',
      number: '03',
      title: 'AI O&G Specialist',
      description: 'Enhances field-to-office workflows, automates data entry from sensor reports, and supports engineering tasks across legacy software. No integration. No blackbox.',
      icon: Zap,
      image: '/images/applications/ai-o-g-specialist.png',
      href: '/og',
      features: [
        'Sensor data processing',
        'Equipment monitoring',
        'Safety compliance',
        'Operational optimization'
      ],
      color: 'from-orange-500 to-red-500'
    }
  ];

  return (
    <section
      id="applications"
      className={`section-padding bg-gradient-to-br from-secondary-50 to-white ${className}`}
    >
      <div className="container-max">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-secondary-900 mb-6">
            Applications
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
            Specialized AI solutions tailored for different industries and use cases, 
            each designed to solve specific challenges and drive measurable results.
          </p>
        </motion.div>

        {/* Applications Grid */}
        <div className="space-y-8">
          {applications.map((app, index) => (
            <motion.div
              key={app.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-white rounded-2xl shadow-xl border border-secondary-200 overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                  {/* Content Side */}
                  <div className="p-8 lg:p-12 flex flex-col justify-center">
                    <div className="flex items-center space-x-4 mb-6">
                      <div className={`w-16 h-16 bg-gradient-to-br ${app.color} rounded-xl flex items-center justify-center text-white font-bold text-xl`}>
                        {app.number}
                      </div>
                      <div>
                        <h3 className="text-2xl lg:text-3xl font-bold text-secondary-900">
                          {app.title}
                        </h3>
                        <div className="flex items-center space-x-2 mt-1">
                          <app.icon className="w-5 h-5 text-secondary-500" />
                          <span className="text-secondary-500 text-sm">Specialized AI Solution</span>
                        </div>
                      </div>
                    </div>

                    <p className="text-lg text-secondary-600 mb-8 leading-relaxed">
                      {app.description}
                    </p>

                    {/* Features List */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-8">
                      {app.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-primary-600 rounded-full" />
                          <span className="text-secondary-700 text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>

                    {/* CTA Button */}
                    <button
                      onClick={() => window.open(`https://energent.ai${app.href}`, '_blank')}
                      className="inline-flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-300 transform hover:scale-105 group-hover:shadow-lg"
                    >
                      <span>Learn More</span>
                      <ExternalLink className="w-4 h-4" />
                    </button>
                  </div>

                  {/* Image Side */}
                  <div className="relative bg-gradient-to-br from-secondary-100 to-primary-100 flex items-center justify-center p-8 lg:p-12">
                    <div className="relative w-full max-w-md">
                      {/* Placeholder for application image */}
                      <div className={`w-full h-64 lg:h-80 bg-gradient-to-br ${app.color} rounded-xl shadow-2xl flex items-center justify-center transform group-hover:scale-105 transition-transform duration-500`}>
                        <app.icon className="w-24 h-24 text-white opacity-80" />
                      </div>
                      
                      {/* Floating Elements */}
                      <div className="absolute -top-4 -right-4 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center">
                        <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
                      </div>
                      
                      <div className="absolute -bottom-4 -left-4 bg-white rounded-lg shadow-lg p-3">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-primary-600 rounded-full" />
                          <span className="text-xs font-medium text-secondary-700">Active</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-primary-600 to-accent-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Ready to Transform Your Business?
            </h3>
            <p className="text-primary-100 mb-6 max-w-2xl mx-auto">
              Discover how our AI applications can revolutionize your industry-specific workflows 
              and drive unprecedented efficiency gains.
            </p>
            <button
              onClick={() => window.open('https://app.energent.ai/', '_blank')}
              className="inline-flex items-center space-x-2 bg-white text-primary-600 font-semibold px-8 py-3 rounded-lg hover:bg-primary-50 transition-all duration-300 transform hover:scale-105"
            >
              <span>Start Free Trial</span>
              <ArrowRight className="w-4 h-4" />
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Applications;
