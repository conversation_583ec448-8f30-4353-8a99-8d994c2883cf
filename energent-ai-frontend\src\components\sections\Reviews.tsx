import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Star, Quote, ChevronLeft, ChevronRight } from 'lucide-react';

interface ReviewsProps {
  className?: string;
}

const Reviews: React.FC<ReviewsProps> = ({ className = '' }) => {
  const [currentReview, setCurrentReview] = useState(0);

  const reviews = [
    {
      id: 'richard-song',
      content: "We had tried all the pdf extraction tool and AnyParser gave us the most accurate results.",
      author: {
        name: '<PERSON>',
        role: 'CEO',
        company: 'Epsilla',
        image: '/images/reviewers/richard-song.png'
      },
      rating: 5
    },
    {
      id: 'ethan-zheng',
      content: "<PERSON><PERSON>arser outperformed 10+ other parsers in our benchmarks, delivering top-tier resume parsing accuracy with the fastest multi-model LLM solution—all while maintaining exceptional performance.",
      author: {
        name: '<PERSON>',
        role: 'CTO',
        company: 'Jobright',
        image: '/images/reviewers/ethan-zheng.png'
      },
      rating: 5
    },
    {
      id: 'jon-conradt',
      content: "<PERSON><PERSON>ars<PERSON>'s advanced multimodal AI delivers where other approaches fail. Complex documents require this fusion of sight and language.",
      author: {
        name: '<PERSON>',
        role: 'Principal Scientist',
        company: 'AWS',
        image: '/images/reviewers/jon-conradt.png'
      },
      rating: 5
    },
    {
      id: 'cass',
      content: "As an AI educator, I seek SOTA solutions for my ML practitioner students. AnyParser enhances retrieval accuracy in document parsing while balancing security, cost, and efficiency—an innovative tool for any pipeline!",
      author: {
        name: 'Cass',
        role: 'Senior Scientist',
        company: 'AWS',
        image: '/images/reviewers/cass.png'
      },
      rating: 5
    },
    {
      id: 'felix-bai',
      content: "I am impressed by AnyParser's innovation in the space of AI and LLM, including the novel methodologies of synthetic data generation, retriever model fine-tuning in RAG, and their open-source products out of those innovations.",
      author: {
        name: 'Felix Bai',
        role: 'Sr. Solution Architect',
        company: 'AWS',
        image: '/images/reviewers/felix-bai.png'
      },
      rating: 5
    },
    {
      id: 'steve-cooper',
      content: "I have validated the quality of AnyParser goes far beyond traditional OCR tools like Langchain / Unstructured. Looking forward to using this in our future projects.",
      author: {
        name: 'Steve Cooper',
        role: 'Cofounder',
        company: 'ai ticker chat',
        image: '/images/reviewers/steve-cooper.png'
      },
      rating: 5
    },
    {
      id: 'jamal',
      content: "It's far better than other tools! Our data analysts are able to triple their outputs.",
      author: {
        name: 'Jamal',
        role: 'CEO',
        company: 'xtrategise',
        image: '/images/reviewers/jamal.png'
      },
      rating: 5
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [reviews.length]);

  const nextReview = () => {
    setCurrentReview((prev) => (prev + 1) % reviews.length);
  };

  const prevReview = () => {
    setCurrentReview((prev) => (prev - 1 + reviews.length) % reviews.length);
  };

  const goToReview = (index: number) => {
    setCurrentReview(index);
  };

  return (
    <section
      id="reviews"
      className={`section-padding bg-white ${className}`}
    >
      <div className="container-max">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-secondary-900 mb-6">
            Reviews
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
            Hear what our clients say about their experience with our AI solutions 
            and the transformative impact on their businesses.
          </p>
        </motion.div>

        {/* Review Carousel */}
        <div className="relative max-w-4xl mx-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentReview}
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -100 }}
              transition={{ duration: 0.5 }}
              className="bg-gradient-to-br from-secondary-50 to-primary-50 rounded-2xl p-8 md:p-12 border border-secondary-200"
            >
              {/* Quote Icon */}
              <div className="flex justify-center mb-6">
                <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center">
                  <Quote className="w-8 h-8 text-white" />
                </div>
              </div>

              {/* Review Content */}
              <blockquote className="text-xl md:text-2xl text-secondary-800 text-center leading-relaxed mb-8 font-medium">
                "{reviews[currentReview].content}"
              </blockquote>

              {/* Rating */}
              <div className="flex justify-center mb-6">
                <div className="flex space-x-1">
                  {[...Array(reviews[currentReview].rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
              </div>

              {/* Author */}
              <div className="flex items-center justify-center space-x-4">
                <div className="w-16 h-16 bg-secondary-300 rounded-full flex items-center justify-center">
                  <span className="text-secondary-600 font-semibold text-lg">
                    {reviews[currentReview].author.name.charAt(0)}
                  </span>
                </div>
                <div className="text-center">
                  <h4 className="font-semibold text-secondary-900">
                    {reviews[currentReview].author.name}
                  </h4>
                  <p className="text-secondary-600">
                    {reviews[currentReview].author.role} - {reviews[currentReview].author.company}
                  </p>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>

          {/* Navigation Buttons */}
          <button
            onClick={prevReview}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white rounded-full shadow-lg border border-secondary-200 flex items-center justify-center hover:bg-secondary-50 transition-colors duration-200"
          >
            <ChevronLeft className="w-6 h-6 text-secondary-600" />
          </button>
          
          <button
            onClick={nextReview}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white rounded-full shadow-lg border border-secondary-200 flex items-center justify-center hover:bg-secondary-50 transition-colors duration-200"
          >
            <ChevronRight className="w-6 h-6 text-secondary-600" />
          </button>
        </div>

        {/* Dots Indicator */}
        <div className="flex justify-center space-x-2 mt-8">
          {reviews.map((_, index) => (
            <button
              key={index}
              onClick={() => goToReview(index)}
              className={`w-3 h-3 rounded-full transition-all duration-200 ${
                index === currentReview
                  ? 'bg-primary-600 scale-125'
                  : 'bg-secondary-300 hover:bg-secondary-400'
              }`}
            />
          ))}
        </div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16"
        >
          <div className="text-center">
            <div className="text-3xl font-bold text-primary-600 mb-2">4.9/5</div>
            <p className="text-secondary-600">Average Rating</p>
          </div>
          
          <div className="text-center">
            <div className="text-3xl font-bold text-primary-600 mb-2">500+</div>
            <p className="text-secondary-600">Happy Customers</p>
          </div>
          
          <div className="text-center">
            <div className="text-3xl font-bold text-primary-600 mb-2">99%</div>
            <p className="text-secondary-600">Would Recommend</p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Reviews;
