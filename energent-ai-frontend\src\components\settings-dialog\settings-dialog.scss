.select-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
  height: 70px;
  & > label {
    font-size: 10px;
  }
}

.settings-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  height: 70px;

  .connected-indicator {
    font-style: italic;
  }

  /*.connected-indicator {
    &:before {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 0;
      display: block;
      content: "";
      width: 100%;
      height: 100%;
      background-color: var(--Neutral-5);
      opacity: 0.85;
    }
    z-index: 1;
    
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 500;
    color: var(--Neutral-80);

    p {
      z-index: 1;
    }
  }*/

  h4 {
    margin-left: 4px;
    margin-bottom: 10px;
  }

  button {
    background: none;
    border: 0;
  }

  .dialog {
    font-family: "Space mono";
    background: var(--Neutral-5);
    border-radius: 18px;
    color: var(--Neutral-80);
    border: 0;
    padding: 0;
    margin: 0;
    position: fixed;
    top: -400px;
    right: 0;
    width: 696px;
    height: 593px;
    transform: translate(-25%, -50%);
  }

  .dialog-container {
    box-sizing: border-box;
    padding: 32px;
    max-height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .small {
    font-size: 80%;
  }

  .mode-selectors {
    display: flex;
    gap: 1rem;
    padding: 8px 0;
  }

  textarea.system {
    border-radius: 12px;
    background-color: var(--Neutral-15);
    color: var(--Neutral-80);
    margin-top: 8px;
    font-family:
      Google Sans,
      sans-serif;
    line-height: 21px;
    font-size: 16px;
    field-sizing: content;
    width: calc(100% - 16px);
    min-height: 150px;
    height: 150px;
    padding: 8px;
    border: 0;
    resize: vertical;
    box-sizing: border-box;
  }

  .function-declarations {
    font-size: 66%;
    width: 100%;
    /* Enables vertical scrolling */
  }

  .fd-rows {
    display: grid;
    grid-template-columns: 1fr 0.5fr 1.5fr;
    row-gap: 6px;
    /* Three columns, last one takes remaining space */
  }

  .fd-row-name {
    font-family: "Space mono";
    font-size: 12px;
    font-weight: bold;
    color: var(--Blue-400);
    font-weight: bold;
    border-radius: 8px;
    border: 1px solid var(--Neutral-20, #2a2f31);
    padding: 10px;
  }

  .fd-row-args {
    padding: 12px;
  }

  .fd-row-args > *:not(:last-child)::after {
    content: ", ";
  }

  .fd-row {
    display: contents;
    color: var(--Neutral-70);
    /* Make data-row participate in the grid layout of data-rows, without being an additional grid row */
    align-items: center;
    /* Vertically center the content of the row */
    /* Bottom border for each row */
    height: 35px;
    /* The row height */
  }

  .fd-row-description {
    flex: 1;
    background: transparent;
    border: none;
    color: inherit;
    font-size: inherit;
    padding: 2px 4px;

    &:hover,
    &:focus {
      background: var(--Neutral-20);
      outline: none;
    }
  }
}
