import { AIProvider, AIConfig, ChatMessage } from '../types';
import OpenAIService from './openai';

export class AIServiceManager {
  private openAIService: OpenAIService | null = null;
  private currentProvider: AIProvider;
  private config: Record<AIProvider, AIConfig>;

  constructor() {
    this.currentProvider = (process.env.REACT_APP_AI_PROVIDER as AIProvider) || 'gemini';
    
    this.config = {
      gemini: {
        provider: 'gemini',
        apiKey: process.env.REACT_APP_GEMINI_API_KEY || '',
        model: 'gemini-1.5-flash',
      },
      openai: {
        provider: 'openai',
        apiKey: process.env.REACT_APP_OPENAI_API_KEY || '',
        model: 'gpt-3.5-turbo',
      },
    };

    this.initializeServices();
  }

  private initializeServices() {
    if (this.config.openai.apiKey) {
      this.openAIService = new OpenAIService(this.config.openai);
    }
  }

  getCurrentProvider(): AIProvider {
    return this.currentProvider;
  }

  setProvider(provider: AIProvider) {
    this.currentProvider = provider;
  }

  getProviderConfig(provider?: AIProvider): AIConfig {
    return this.config[provider || this.currentProvider];
  }

  isProviderAvailable(provider: AIProvider): boolean {
    const config = this.config[provider];
    return !!config.apiKey;
  }

  async sendMessage(messages: ChatMessage[], provider?: AIProvider): Promise<string> {
    const targetProvider = provider || this.currentProvider;
    
    if (!this.isProviderAvailable(targetProvider)) {
      throw new Error(`${targetProvider} is not configured. Please check your API keys.`);
    }

    switch (targetProvider) {
      case 'openai':
        if (!this.openAIService) {
          throw new Error('OpenAI service not initialized');
        }
        return await this.openAIService.sendMessage(messages);
      
      case 'gemini':
        // For Gemini, we'll use the existing Live API context
        // This is handled by the LiveAPIContext component
        throw new Error('Gemini messages should be handled through LiveAPIContext');
      
      default:
        throw new Error(`Unsupported provider: ${targetProvider}`);
    }
  }

  async sendStreamMessage(
    messages: ChatMessage[],
    onChunk: (chunk: string) => void,
    provider?: AIProvider
  ): Promise<void> {
    const targetProvider = provider || this.currentProvider;
    
    if (!this.isProviderAvailable(targetProvider)) {
      throw new Error(`${targetProvider} is not configured. Please check your API keys.`);
    }

    switch (targetProvider) {
      case 'openai':
        if (!this.openAIService) {
          throw new Error('OpenAI service not initialized');
        }
        return await this.openAIService.sendStreamMessage(messages, onChunk);
      
      case 'gemini':
        throw new Error('Gemini streaming should be handled through LiveAPIContext');
      
      default:
        throw new Error(`Unsupported provider: ${targetProvider}`);
    }
  }

  async transcribeAudio(audioBlob: Blob, provider?: AIProvider): Promise<string> {
    const targetProvider = provider || this.currentProvider;
    
    if (!this.isProviderAvailable(targetProvider)) {
      throw new Error(`${targetProvider} is not configured. Please check your API keys.`);
    }

    switch (targetProvider) {
      case 'openai':
        if (!this.openAIService) {
          throw new Error('OpenAI service not initialized');
        }
        return await this.openAIService.transcribeAudio(audioBlob);
      
      case 'gemini':
        throw new Error('Gemini transcription should be handled through LiveAPIContext');
      
      default:
        throw new Error(`Unsupported provider: ${targetProvider}`);
    }
  }

  async generateSpeech(text: string, provider?: AIProvider): Promise<ArrayBuffer> {
    const targetProvider = provider || this.currentProvider;
    
    if (!this.isProviderAvailable(targetProvider)) {
      throw new Error(`${targetProvider} is not configured. Please check your API keys.`);
    }

    switch (targetProvider) {
      case 'openai':
        if (!this.openAIService) {
          throw new Error('OpenAI service not initialized');
        }
        return await this.openAIService.generateSpeech(text);
      
      case 'gemini':
        throw new Error('Gemini speech generation should be handled through LiveAPIContext');
      
      default:
        throw new Error(`Unsupported provider: ${targetProvider}`);
    }
  }

  async startVoiceChat(
    onMessage: (message: string) => void,
    onError: (error: Error) => void,
    provider?: AIProvider
  ): Promise<() => void> {
    const targetProvider = provider || this.currentProvider;
    
    if (!this.isProviderAvailable(targetProvider)) {
      throw new Error(`${targetProvider} is not configured. Please check your API keys.`);
    }

    switch (targetProvider) {
      case 'openai':
        if (!this.openAIService) {
          throw new Error('OpenAI service not initialized');
        }
        return await this.openAIService.startVoiceChat(onMessage, onError);
      
      case 'gemini':
        throw new Error('Gemini voice chat should be handled through LiveAPIContext');
      
      default:
        throw new Error(`Unsupported provider: ${targetProvider}`);
    }
  }

  getAvailableProviders(): AIProvider[] {
    return Object.keys(this.config).filter(provider => 
      this.isProviderAvailable(provider as AIProvider)
    ) as AIProvider[];
  }

  validateConfiguration(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    Object.entries(this.config).forEach(([provider, config]) => {
      if (!config.apiKey) {
        errors.push(`Missing API key for ${provider}`);
      }
    });

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

// Singleton instance
export const aiService = new AIServiceManager();
export default aiService;
