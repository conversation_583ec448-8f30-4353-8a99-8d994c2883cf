import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'lucide-react';

interface HeroProps {
  className?: string;
}

const Hero: React.FC<HeroProps> = ({ className = '' }) => {
  const handleScrollToAIHub = () => {
    const element = document.querySelector('#ai-hub');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleTryForFree = () => {
    // In a real app, this would navigate to the signup/trial page
    window.open('https://app.energent.ai/', '_blank');
  };

  return (
    <section
      id="home"
      className={`relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-secondary-50 via-white to-primary-50 ${className}`}
    >
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-accent-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float" style={{ animationDelay: '2s' }} />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-secondary-200 rounded-full mix-blend-multiply filter blur-xl opacity-50 animate-float" style={{ animationDelay: '4s' }} />
      </div>

      {/* Grid Pattern */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23e2e8f0" fill-opacity="0.3"%3E%3Ccircle cx="30" cy="30" r="1.5"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40" />

      <div className="relative z-10 container-max section-padding text-center">
        <div className="max-w-4xl mx-auto">
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center space-x-2 bg-white/80 backdrop-blur-sm border border-primary-200 rounded-full px-4 py-2 mb-8"
          >
            <Sparkles className="w-4 h-4 text-primary-600" />
            <span className="text-sm font-medium text-primary-700">
              AI Agent turns data into actionable insights
            </span>
          </motion.div>

          {/* Main Heading */}
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-4xl md:text-6xl lg:text-7xl font-bold text-secondary-900 mb-6 leading-tight"
          >
            No-code, no integration.{' '}
            <span className="bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">
              Automation accessible
            </span>{' '}
            without tech expertise.
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-xl md:text-2xl text-secondary-600 mb-12 max-w-3xl mx-auto leading-relaxed"
          >
            Transform your business with intelligent AI agents that understand your data, 
            automate complex workflows, and deliver insights that drive real results.
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 mb-16"
          >
            <button
              onClick={handleTryForFree}
              className="group bg-primary-600 hover:bg-primary-700 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-xl flex items-center space-x-2"
            >
              <span>Try for Free</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
            </button>
            
            <button
              onClick={handleScrollToAIHub}
              className="group bg-white/80 backdrop-blur-sm hover:bg-white border border-secondary-300 text-secondary-700 font-semibold px-8 py-4 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg flex items-center space-x-2"
            >
              <Play className="w-5 h-5" />
              <span>Explore AI Hub</span>
            </button>
          </motion.div>

          {/* Feature Highlights */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
          >
            <div className="flex items-center justify-center space-x-3 bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-secondary-200">
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                <Brain className="w-6 h-6 text-primary-600" />
              </div>
              <div className="text-left">
                <h3 className="font-semibold text-secondary-900">Smart AI</h3>
                <p className="text-sm text-secondary-600">Intelligent automation</p>
              </div>
            </div>

            <div className="flex items-center justify-center space-x-3 bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-secondary-200">
              <div className="w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center">
                <Zap className="w-6 h-6 text-accent-600" />
              </div>
              <div className="text-left">
                <h3 className="font-semibold text-secondary-900">No-Code</h3>
                <p className="text-sm text-secondary-600">Easy to implement</p>
              </div>
            </div>

            <div className="flex items-center justify-center space-x-3 bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-secondary-200">
              <div className="w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center">
                <Sparkles className="w-6 h-6 text-secondary-600" />
              </div>
              <div className="text-left">
                <h3 className="font-semibold text-secondary-900">Real-time</h3>
                <p className="text-sm text-secondary-600">Instant insights</p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1.2 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <div className="w-6 h-10 border-2 border-secondary-400 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-secondary-400 rounded-full mt-2 animate-bounce" />
        </div>
      </motion.div>
    </section>
  );
};

export default Hero;
