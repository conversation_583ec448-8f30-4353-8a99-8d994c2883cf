import OpenAI from 'openai';
import { AIConfig, ChatMessage } from '../types';

export class OpenAIService {
  private client: OpenAI;
  private config: AIConfig;

  constructor(config: AIConfig) {
    this.config = config;
    this.client = new OpenAI({
      apiKey: config.apiKey,
      dangerouslyAllowBrowser: true, // Note: In production, use a backend proxy
    });
  }

  async sendMessage(messages: ChatMessage[]): Promise<string> {
    try {
      const openAIMessages = messages.map(msg => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content,
      }));

      const response = await this.client.chat.completions.create({
        model: this.config.model || 'gpt-3.5-turbo',
        messages: openAIMessages,
        temperature: 0.7,
        max_tokens: 1000,
      });

      return response.choices[0]?.message?.content || 'No response received';
    } catch (error) {
      console.error('OpenAI API error:', error);
      throw new Error('Failed to get response from OpenAI');
    }
  }

  async sendStreamMessage(
    messages: ChatMessage[],
    onChunk: (chunk: string) => void
  ): Promise<void> {
    try {
      const openAIMessages = messages.map(msg => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content,
      }));

      const stream = await this.client.chat.completions.create({
        model: this.config.model || 'gpt-3.5-turbo',
        messages: openAIMessages,
        temperature: 0.7,
        max_tokens: 1000,
        stream: true,
      });

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content;
        if (content) {
          onChunk(content);
        }
      }
    } catch (error) {
      console.error('OpenAI streaming error:', error);
      throw new Error('Failed to stream response from OpenAI');
    }
  }

  async transcribeAudio(audioBlob: Blob): Promise<string> {
    try {
      const file = new File([audioBlob], 'audio.wav', { type: 'audio/wav' });
      
      const response = await this.client.audio.transcriptions.create({
        file: file,
        model: 'whisper-1',
      });

      return response.text;
    } catch (error) {
      console.error('OpenAI transcription error:', error);
      throw new Error('Failed to transcribe audio');
    }
  }

  async generateSpeech(text: string): Promise<ArrayBuffer> {
    try {
      const response = await this.client.audio.speech.create({
        model: 'tts-1',
        voice: 'alloy',
        input: text,
      });

      return await response.arrayBuffer();
    } catch (error) {
      console.error('OpenAI speech generation error:', error);
      throw new Error('Failed to generate speech');
    }
  }

  // Voice chat simulation (since OpenAI doesn't have real-time voice like Gemini)
  async startVoiceChat(
    onMessage: (message: string) => void,
    onError: (error: Error) => void
  ): Promise<() => void> {
    let isActive = true;
    let mediaRecorder: MediaRecorder | null = null;
    let audioChunks: Blob[] = [];

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      mediaRecorder = new MediaRecorder(stream);
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunks.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        if (audioChunks.length > 0 && isActive) {
          try {
            const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
            const transcription = await this.transcribeAudio(audioBlob);
            
            if (transcription.trim()) {
              // Send transcription to chat API
              const response = await this.sendMessage([
                { id: Date.now().toString(), content: transcription, role: 'user', timestamp: new Date() }
              ]);
              
              onMessage(response);
              
              // Generate speech response
              const speechBuffer = await this.generateSpeech(response);
              const audioContext = new AudioContext();
              const audioBuffer = await audioContext.decodeAudioData(speechBuffer);
              const source = audioContext.createBufferSource();
              source.buffer = audioBuffer;
              source.connect(audioContext.destination);
              source.start();
            }
          } catch (error) {
            onError(error as Error);
          }
        }
        audioChunks = [];
      };

      // Record in chunks for continuous processing
      const recordChunk = () => {
        if (isActive && mediaRecorder?.state === 'inactive') {
          mediaRecorder.start();
          setTimeout(() => {
            if (isActive && mediaRecorder?.state === 'recording') {
              mediaRecorder.stop();
              setTimeout(recordChunk, 100); // Small delay before next chunk
            }
          }, 3000); // 3-second chunks
        }
      };

      recordChunk();

      // Return cleanup function
      return () => {
        isActive = false;
        if (mediaRecorder?.state === 'recording') {
          mediaRecorder.stop();
        }
        stream.getTracks().forEach(track => track.stop());
      };

    } catch (error) {
      onError(error as Error);
      return () => {};
    }
  }
}

export default OpenAIService;
