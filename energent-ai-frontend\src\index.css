@tailwind base;
@tailwind components;
@tailwind utilities;

@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap");

@layer base {
    html {
        scroll-behavior: smooth;
    }

    body {
        @apply font-sans antialiased;
        margin: 0;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    * {
        @apply border-border;
    }
}

@layer components {
    .btn-primary {
        @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
    }

    .btn-secondary {
        @apply bg-secondary-200 hover:bg-secondary-300 text-secondary-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
    }

    .btn-outline {
        @apply border border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-2 px-4 rounded-lg transition-all duration-200;
    }

    .card {
        @apply bg-white rounded-xl shadow-lg border border-secondary-200 p-6;
    }

    .section-padding {
        @apply py-16 px-4 sm:px-6 lg:px-8;
    }

    .container-max {
        @apply max-w-7xl mx-auto;
    }
}
